<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hotel Zocalo Campestre - GoVoy</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8fafc;
            line-height: 1.6;
            color: #1e293b;
        }

        /* Header estilo GoVoy */
        .header {
            background: linear-gradient(135deg, rgba(0,0,0,0.4), rgba(0,0,0,0.6)), url('https://images.unsplash.com/photo-1566073771259-6a8506099945?w=1200&h=300&fit=crop') center/cover;
            color: white;
            padding: 20px 0;
            min-height: 25vh;
            position: relative;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(255, 255, 255, 0.95);
            padding: 15px 30px;
            border-radius: 12px;
            backdrop-filter: blur(10px);
            margin-bottom: 30px;
        }

        .logo {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
        }

        .logo .go {
            color: #06b6d4;
        }

        .logo .voy {
            color: #f59e0b;
        }

        .nav-links {
            display: flex;
            gap: 30px;
        }

        .nav-links a {
            color: #64748b;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.3s;
            font-weight: 500;
        }

        .nav-links a:hover {
            background-color: #f1f5f9;
            color: #1e293b;
        }

        .back-btn {
            background: linear-gradient(135deg, #a855f7, #8b5cf6);
            color: white !important;
            padding: 10px 20px !important;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s;
            box-shadow: 0 4px 12px rgba(168, 85, 247, 0.3);
        }

        .back-btn:hover {
            background: linear-gradient(135deg, #9333ea, #7c3aed) !important;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(168, 85, 247, 0.4);
        }

        .hero-title {
            text-align: center;
            color: white;
            font-size: 42px;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            margin-bottom: 10px;
        }

        .hero-subtitle {
            text-align: center;
            color: rgba(255,255,255,0.9);
            font-size: 18px;
            font-weight: 500;
        }

        /* Main Content Layout */
        .main-layout {
            display: grid;
            grid-template-columns: 1fr 350px;
            gap: 30px;
            margin-top: -50px;
            position: relative;
            z-index: 10;
        }

        /* Hotel Card */
        .hotel-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 1px solid #f1f5f9;
        }

        .hotel-gallery {
            position: relative;
            height: 350px;
            background: url('https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800&h=600&fit=crop') center/cover;
        }

        .gallery-overlay {
            position: absolute;
            bottom: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
        }

        .gallery-thumb {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            background-size: cover;
            background-position: center;
            border: 3px solid white;
            cursor: pointer;
            transition: all 0.3s;
        }

        .gallery-thumb:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }

        .gallery-thumb:nth-child(1) {
            background-image: url('https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=400&h=300&fit=crop');
        }

        .gallery-thumb:nth-child(2) {
            background-image: url('https://images.unsplash.com/photo-1564501049412-61c2a3083791?w=400&h=300&fit=crop');
        }

        .gallery-thumb:nth-child(3) {
            background-image: url('https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?w=400&h=300&fit=crop');
        }

        .hotel-info {
            padding: 30px;
        }

        .hotel-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
        }

        .hotel-title {
            font-size: 32px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .hotel-location {
            color: #64748b;
            font-size: 16px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .location-icon {
            width: 16px;
            height: 16px;
            background: linear-gradient(135deg, #06b6d4, #0891b2);
            border-radius: 50%;
        }

        .hotel-rating {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .rating-score {
            background: linear-gradient(135deg, #a855f7, #8b5cf6);
            color: white;
            padding: 10px 15px;
            border-radius: 12px;
            font-weight: 700;
            font-size: 18px;
            box-shadow: 0 4px 12px rgba(168, 85, 247, 0.3);
        }

        .rating-text {
            color: #64748b;
            font-weight: 500;
            font-size: 16px;
        }

        .quick-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .action-btn {
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            color: #0369a1;
            border: 1px solid #bae6fd;
            padding: 10px 20px;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }

        .action-btn:hover {
            background: linear-gradient(135deg, #e0f2fe, #bae6fd);
            transform: translateY(-2px);
        }

        /* Booking Sidebar */
        .booking-sidebar {
            position: sticky;
            top: 20px;
            height: fit-content;
        }

        .booking-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 1px solid #f1f5f9;
        }

        .booking-header {
            background: linear-gradient(135deg, #a855f7, #8b5cf6);
            color: white;
            padding: 25px;
            text-align: center;
        }

        .price-display {
            font-size: 42px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .price-period {
            font-size: 16px;
            opacity: 0.9;
        }

        .booking-form {
            padding: 25px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            display: block;
        }

        .form-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s;
            background: white;
        }

        .form-input:focus {
            outline: none;
            border-color: #a855f7;
            box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.1);
        }

        .booking-summary {
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
            color: #64748b;
        }

        .summary-total {
            font-weight: 700;
            font-size: 18px;
            color: #1e293b;
            border-top: 2px solid #e2e8f0;
            padding-top: 12px;
            margin-top: 12px;
        }

        .reserve-btn {
            width: 100%;
            background: linear-gradient(135deg, #f59e0b, #f97316);
            color: white;
            border: none;
            padding: 18px;
            border-radius: 12px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
        }

        .reserve-btn:hover {
            background: linear-gradient(135deg, #ea580c, #dc2626);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(245, 158, 11, 0.4);
        }

        .booking-benefits {
            padding: 20px 25px;
            background: #f8fafc;
            text-align: center;
        }

        .benefit-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin-bottom: 8px;
            color: #64748b;
            font-size: 14px;
        }

        .benefit-icon {
            width: 16px;
            height: 16px;
            background: linear-gradient(135deg, #10b981, #059669);
            border-radius: 50%;
        }

        /* Content Sections */
        .content-tabs {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            margin-top: 30px;
            overflow: hidden;
            border: 1px solid #f1f5f9;
        }

        .tab-navigation {
            display: flex;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
        }

        .tab-btn {
            flex: 1;
            padding: 20px;
            background: none;
            border: none;
            font-size: 16px;
            font-weight: 600;
            color: #64748b;
            cursor: pointer;
            transition: all 0.3s;
            border-bottom: 3px solid transparent;
        }

        .tab-btn.active {
            color: #a855f7;
            background: white;
            border-bottom-color: #a855f7;
        }

        .tab-content {
            padding: 30px;
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .section-title {
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .title-icon {
            width: 24px;
            height: 24px;
            background: linear-gradient(135deg, #a855f7, #8b5cf6);
            border-radius: 6px;
        }

        .description {
            color: #475569;
            line-height: 1.8;
            font-size: 16px;
            margin-bottom: 20px;
        }

        .amenities-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .amenity-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 15px;
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            transition: all 0.3s;
        }

        .amenity-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .amenity-icon {
            width: 24px;
            height: 24px;
            background: linear-gradient(135deg, #06b6d4, #0891b2);
            border-radius: 8px;
        }

        .amenity-text {
            color: #374151;
            font-weight: 600;
        }

        /* Reviews */
        .reviews-grid {
            display: grid;
            gap: 20px;
            margin-top: 20px;
        }

        .review-item {
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            padding: 25px;
            border-radius: 16px;
            border: 1px solid #e2e8f0;
        }

        .review-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .reviewer-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .reviewer-avatar {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #a855f7, #8b5cf6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
        }

        .reviewer-name {
            font-weight: 600;
            color: #1e293b;
        }

        .review-rating {
            background: linear-gradient(135deg, #a855f7, #8b5cf6);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
        }

        .review-text {
            color: #475569;
            line-height: 1.7;
            font-style: italic;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .main-layout {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .booking-sidebar {
                position: static;
                order: -1;
            }
        }

        @media (max-width: 768px) {
            .header {
                min-height: 20vh;
            }

            .nav {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .hero-title {
                font-size: 32px;
            }

            .main-layout {
                margin-top: -30px;
                gap: 15px;
            }

            .hotel-gallery {
                height: 250px;
            }

            .gallery-overlay {
                display: none;
            }

            .hotel-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .quick-actions {
                justify-content: center;
            }

            .tab-navigation {
                flex-direction: column;
            }

            .amenities-grid {
                grid-template-columns: 1fr;
            }

            .booking-header {
                padding: 20px;
            }

            .price-display {
                font-size: 32px;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 0 15px;
            }

            .hotel-info {
                padding: 20px;
            }

            .booking-form {
                padding: 20px;
            }

            .hero-title {
                font-size: 28px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="nav">
                <div class="logo"><span class="go">Go</span><span class="voy">Voy</span></div>
                <div class="nav-links">
                    <a href="index.html" class="back-btn">← Volver a resultados</a>
                </div>
            </nav>
            <h1 class="hero-title">Hotel Zocalo Campestre</h1>
            <p class="hero-subtitle">Experiencia única en Guatapé</p>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container">
        <div class="main-layout">
            <!-- Hotel Card -->
            <div class="hotel-card">
                <div class="hotel-gallery">
                    <div class="gallery-overlay">
                        <div class="gallery-thumb"></div>
                        <div class="gallery-thumb"></div>
                        <div class="gallery-thumb"></div>
                    </div>
                </div>

                <div class="hotel-info">
                    <div class="hotel-header">
                        <div>
                            <h2 class="hotel-title">Hotel Zocalo Campestre</h2>
                            <p class="hotel-location">
                                <span class="location-icon"></span>
                                Guatapé, Antioquia • Mostrar en el mapa • a 2.1 km del centro
                            </p>
                            <div class="hotel-rating">
                                <span class="rating-score">9.2</span>
                                <span class="rating-text">Fantástico • 478 comentarios</span>
                            </div>
                        </div>
                    </div>

                    <div class="quick-actions">
                        <button class="action-btn">📍 Ver ubicación</button>
                        <button class="action-btn">📷 Ver fotos</button>
                        <button class="action-btn">⭐ Reseñas</button>
                    </div>
                </div>
            </div>

            <!-- Content Tabs -->
            <div class="content-tabs">
                <div class="tab-navigation">
                    <button class="tab-btn active" onclick="showTab('description')">Descripción</button>
                    <button class="tab-btn" onclick="showTab('amenities')">Servicios</button>
                    <button class="tab-btn" onclick="showTab('reviews')">Reseñas</button>
                </div>

                <!-- Description Tab -->
                <div class="tab-content active" id="description">
                    <h2 class="section-title">
                        <div class="title-icon"></div>
                        Acerca de este alojamiento
                    </h2>
                    <p class="description">
                        El Hotel Zocalo Campestre se encuentra en Guatapé, a 70 km de Medellín. Ofrece restaurante, WiFi gratuita y bar.
                        Este establecimiento dispone de habitaciones en la zona principal y bungalows independientes rodeados de jardines
                        tropicales. Todas las habitaciones incluyen TV de pantalla plana, aire acondicionado y baño privado.
                    </p>
                    <p class="description">
                        El hotel cuenta con una hermosa piscina al aire libre con vistas panorámicas al embalse de Guatapé.
                        Los huéspedes pueden disfrutar de actividades como senderismo, pesca y deportes acuáticos.
                        El restaurante sirve cocina local e internacional, y el bar ofrece cócteles y bebidas refrescantes.
                    </p>
                </div>

                <!-- Amenities Tab -->
                <div class="tab-content" id="amenities">
                    <h2 class="section-title">
                        <div class="title-icon"></div>
                        Servicios más populares
                    </h2>
                    <div class="amenities-grid">
                        <div class="amenity-item">
                            <div class="amenity-icon"></div>
                            <span class="amenity-text">WiFi gratis</span>
                        </div>
                        <div class="amenity-item">
                            <div class="amenity-icon"></div>
                            <span class="amenity-text">Piscina al aire libre</span>
                        </div>
                        <div class="amenity-item">
                            <div class="amenity-icon"></div>
                            <span class="amenity-text">Parking gratuito</span>
                        </div>
                        <div class="amenity-item">
                            <div class="amenity-icon"></div>
                            <span class="amenity-text">Restaurante</span>
                        </div>
                        <div class="amenity-item">
                            <div class="amenity-icon"></div>
                            <span class="amenity-text">Bar</span>
                        </div>
                        <div class="amenity-item">
                            <div class="amenity-icon"></div>
                            <span class="amenity-text">Aire acondicionado</span>
                        </div>
                        <div class="amenity-item">
                            <div class="amenity-icon"></div>
                            <span class="amenity-text">TV de pantalla plana</span>
                        </div>
                        <div class="amenity-item">
                            <div class="amenity-icon"></div>
                            <span class="amenity-text">Jardín</span>
                        </div>
                        <div class="amenity-item">
                            <div class="amenity-icon"></div>
                            <span class="amenity-text">Terraza</span>
                        </div>
                        <div class="amenity-item">
                            <div class="amenity-icon"></div>
                            <span class="amenity-text">Servicio de habitaciones</span>
                        </div>
                    </div>
                </div>

                <!-- Reviews Tab -->
                <div class="tab-content" id="reviews">
                    <h2 class="section-title">
                        <div class="title-icon"></div>
                        Comentarios de huéspedes
                    </h2>
                    <div class="reviews-grid">
                        <div class="review-item">
                            <div class="review-header">
                                <div class="reviewer-info">
                                    <div class="reviewer-avatar">M</div>
                                    <span class="reviewer-name">María González</span>
                                </div>
                                <span class="review-rating">9.5</span>
                            </div>
                            <p class="review-text">
                                "Excelente hotel con vistas espectaculares al embalse. Las habitaciones son muy cómodas y el personal
                                extremadamente amable. La piscina es perfecta para relajarse y el restaurante tiene comida deliciosa.
                                Definitivamente regresaríamos."
                            </p>
                        </div>

                        <div class="review-item">
                            <div class="review-header">
                                <div class="reviewer-info">
                                    <div class="reviewer-avatar">C</div>
                                    <span class="reviewer-name">Carlos Rodríguez</span>
                                </div>
                                <span class="review-rating">9.0</span>
                            </div>
                            <p class="review-text">
                                "Ubicación perfecta para explorar Guatapé. El hotel está muy bien mantenido y las instalaciones
                                son de primera calidad. El desayuno es variado y delicioso. Muy recomendado para familias."
                            </p>
                        </div>

                        <div class="review-item">
                            <div class="review-header">
                                <div class="reviewer-info">
                                    <div class="reviewer-avatar">A</div>
                                    <span class="reviewer-name">Ana Martínez</span>
                                </div>
                                <span class="review-rating">8.8</span>
                            </div>
                            <p class="review-text">
                                "Hotel muy tranquilo y relajante. Perfecto para desconectarse de la ciudad.
                                Las vistas desde la piscina son increíbles, especialmente al atardecer.
                                El WiFi funciona muy bien en todas las áreas."
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Booking Sidebar -->
            <div class="booking-sidebar">
                <div class="booking-card">
                    <div class="booking-header">
                        <div class="price-display">$85.000</div>
                        <div class="price-period">por noche</div>
                    </div>

                    <form class="booking-form" id="bookingForm">
                        <div class="form-group">
                            <label class="form-label">Fecha de entrada</label>
                            <input type="date" class="form-input" id="checkinDate" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Fecha de salida</label>
                            <input type="date" class="form-input" id="checkoutDate" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Huéspedes</label>
                            <select class="form-input" id="guestCount">
                                <option value="1">1 adulto</option>
                                <option value="2" selected>2 adultos</option>
                                <option value="3">3 adultos</option>
                                <option value="4">4 adultos</option>
                                <option value="5">5 adultos</option>
                                <option value="6">6 adultos</option>
                            </select>
                        </div>

                        <div class="booking-summary">
                            <div class="summary-row">
                                <span>$85.000 x <span id="nightCount">1</span> noche(s)</span>
                                <span id="subtotal">$85.000</span>
                            </div>
                            <div class="summary-row">
                                <span>Impuestos y tasas</span>
                                <span id="taxes">$8.500</span>
                            </div>
                            <div class="summary-row summary-total">
                                <span>Total</span>
                                <span id="total">$93.500</span>
                            </div>
                        </div>

                        <button type="submit" class="reserve-btn">
                            Reservar ahora
                        </button>
                    </form>

                    <div class="booking-benefits">
                        <div class="benefit-item">
                            <div class="benefit-icon"></div>
                            <span>Cancelación gratuita</span>
                        </div>
                        <div class="benefit-item">
                            <div class="benefit-icon"></div>
                            <span>Sin pagos por adelantado</span>
                        </div>
                        <div class="benefit-item">
                            <div class="benefit-icon"></div>
                            <span>Confirmación inmediata</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Función para cambiar pestañas
        function showTab(tabName) {
            // Ocultar todas las pestañas
            const tabs = document.querySelectorAll('.tab-content');
            tabs.forEach(tab => tab.classList.remove('active'));

            // Remover clase active de todos los botones
            const buttons = document.querySelectorAll('.tab-btn');
            buttons.forEach(btn => btn.classList.remove('active'));

            // Mostrar la pestaña seleccionada
            document.getElementById(tabName).classList.add('active');

            // Activar el botón correspondiente
            event.target.classList.add('active');
        }

        // Configurar fechas por defecto y funcionalidad
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);

            document.getElementById('checkinDate').value = today.toISOString().split('T')[0];
            document.getElementById('checkoutDate').value = tomorrow.toISOString().split('T')[0];

            // Calcular precios dinámicamente
            function updatePricing() {
                const checkin = new Date(document.getElementById('checkinDate').value);
                const checkout = new Date(document.getElementById('checkoutDate').value);
                const nights = Math.max(1, Math.ceil((checkout - checkin) / (1000 * 60 * 60 * 24)));

                const pricePerNight = 85000;
                const subtotal = pricePerNight * nights;
                const taxes = Math.round(subtotal * 0.1);
                const total = subtotal + taxes;

                document.getElementById('nightCount').textContent = nights;
                document.getElementById('subtotal').textContent = '$' + subtotal.toLocaleString();
                document.getElementById('taxes').textContent = '$' + taxes.toLocaleString();
                document.getElementById('total').textContent = '$' + total.toLocaleString();
            }

            // Event listeners para actualizar precios
            document.getElementById('checkinDate').addEventListener('change', updatePricing);
            document.getElementById('checkoutDate').addEventListener('change', updatePricing);

            // Inicializar precios
            updatePricing();

            // Manejar envío del formulario
            document.getElementById('bookingForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const checkin = document.getElementById('checkinDate').value;
                const checkout = document.getElementById('checkoutDate').value;
                const guests = document.getElementById('guestCount').value;
                const total = document.getElementById('total').textContent;

                alert(`GoVoy - Reserva confirmada!\n\nHotel: Hotel Zocalo Campestre\nFechas: ${checkin} al ${checkout}\nHuéspedes: ${guests}\nTotal: ${total}\n\n¡Gracias por elegir GoVoy!`);
            });

            // Funcionalidad para botones de acción rápida
            document.querySelectorAll('.action-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const text = this.textContent;
                    if (text.includes('ubicación')) {
                        alert('Abriendo mapa de ubicación...');
                    } else if (text.includes('fotos')) {
                        alert('Abriendo galería de fotos...');
                    } else if (text.includes('Reseñas')) {
                        showTab('reviews');
                        document.querySelector('.content-tabs').scrollIntoView({ behavior: 'smooth' });
                    }
                });
            });

            // Animación de entrada para las tarjetas
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            });

            document.querySelectorAll('.hotel-card, .booking-card, .content-tabs').forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(20px)';
                el.style.transition = 'all 0.6s ease';
                observer.observe(el);
            });
        });
    </script>
</body>
</html>