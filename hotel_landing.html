<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hotel Zocalo Campestre - GoVoy</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8fafc;
            line-height: 1.6;
        }

        /* Header */
        .header {
            background: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
        }

        .logo {
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
        }

        .logo .go {
            color: #06b6d4;
        }

        .logo .voy {
            color: #f59e0b;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: #64748b;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.3s;
            font-weight: 500;
        }

        .nav-links a:hover {
            background-color: #f1f5f9;
            color: #1e293b;
        }

        .back-btn {
            background: linear-gradient(135deg, #a855f7, #8b5cf6);
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(168, 85, 247, 0.3);
        }

        /* Hero Section */
        .hero-section {
            background: white;
            margin-bottom: 30px;
        }

        .hotel-gallery {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr;
            grid-template-rows: 200px 200px;
            gap: 8px;
            height: 400px;
            border-radius: 16px;
            overflow: hidden;
            margin-bottom: 30px;
        }

        .gallery-main {
            grid-row: 1 / 3;
            background: url('https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800&h=600&fit=crop') center/cover;
        }

        .gallery-item {
            background-size: cover;
            background-position: center;
        }

        .gallery-item:nth-child(2) {
            background-image: url('https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=400&h=300&fit=crop');
        }

        .gallery-item:nth-child(3) {
            background-image: url('https://images.unsplash.com/photo-1564501049412-61c2a3083791?w=400&h=300&fit=crop');
        }

        .gallery-item:nth-child(4) {
            background-image: url('https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?w=400&h=300&fit=crop');
        }

        .gallery-item:nth-child(5) {
            background-image: url('https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=400&h=300&fit=crop');
        }

        /* Hotel Info */
        .hotel-header {
            display: flex;
            justify-content: space-between;
            align-items: start;
            margin-bottom: 20px;
        }

        .hotel-title {
            font-size: 32px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .hotel-location {
            color: #64748b;
            font-size: 16px;
            margin-bottom: 15px;
        }

        .hotel-rating {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .rating-score {
            background: linear-gradient(135deg, #a855f7, #8b5cf6);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-weight: 700;
            font-size: 16px;
        }

        .rating-text {
            color: #64748b;
            font-weight: 500;
        }

        .price-section {
            text-align: right;
        }

        .price-amount {
            font-size: 36px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 5px;
        }

        .price-period {
            color: #64748b;
            font-size: 14px;
            margin-bottom: 15px;
        }

        .reserve-btn {
            background: linear-gradient(135deg, #f59e0b, #f97316);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
        }

        .reserve-btn:hover {
            background: linear-gradient(135deg, #ea580c, #dc2626);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(245, 158, 11, 0.4);
        }

        /* Main Content */
        .main-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }

        .content-section {
            background: white;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            margin-bottom: 20px;
            border: 1px solid #f1f5f9;
        }

        .section-title {
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 20px;
            border-bottom: 2px solid #a855f7;
            padding-bottom: 10px;
        }

        .description {
            color: #475569;
            line-height: 1.8;
            font-size: 16px;
        }

        .amenities-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .amenity-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            background: #f8fafc;
            border-radius: 8px;
        }

        .amenity-icon {
            width: 20px;
            height: 20px;
            background: linear-gradient(135deg, #06b6d4, #0891b2);
            border-radius: 50%;
        }

        .amenity-text {
            color: #374151;
            font-weight: 500;
        }

        /* Reviews */
        .review-item {
            border-bottom: 1px solid #f1f5f9;
            padding: 20px 0;
        }

        .review-item:last-child {
            border-bottom: none;
        }

        .review-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .reviewer-name {
            font-weight: 600;
            color: #1e293b;
        }

        .review-rating {
            background: linear-gradient(135deg, #a855f7, #8b5cf6);
            color: white;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
        }

        .review-text {
            color: #475569;
            line-height: 1.6;
        }

        /* Sidebar */
        .sidebar {
            position: sticky;
            top: 100px;
            height: fit-content;
        }

        .booking-card {
            background: white;
            padding: 25px;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #f1f5f9;
        }

        .booking-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 5px;
        }

        .form-input {
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-input:focus {
            outline: none;
            border-color: #a855f7;
        }

        .booking-summary {
            background: #f8fafc;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .summary-total {
            font-weight: 700;
            font-size: 18px;
            color: #1e293b;
            border-top: 1px solid #e2e8f0;
            padding-top: 10px;
        }

        @media (max-width: 768px) {
            .hotel-gallery {
                grid-template-columns: 1fr;
                grid-template-rows: 250px;
                height: 250px;
            }

            .gallery-item:not(.gallery-main) {
                display: none;
            }

            .main-content {
                grid-template-columns: 1fr;
            }

            .hotel-header {
                flex-direction: column;
                gap: 20px;
            }

            .price-section {
                text-align: left;
            }

            .sidebar {
                position: static;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="nav">
                <div class="logo"><span class="go">Go</span><span class="voy">Voy</span></div>
                <div class="nav-links">
                    <a href="index.html" class="back-btn">← Volver a resultados</a>
                </div>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container">
        <!-- Hero Section -->
        <section class="hero-section">
            <div class="hotel-gallery">
                <div class="gallery-main"></div>
                <div class="gallery-item"></div>
                <div class="gallery-item"></div>
                <div class="gallery-item"></div>
                <div class="gallery-item"></div>
            </div>

            <div class="hotel-header">
                <div class="hotel-info">
                    <h1 class="hotel-title">Hotel Zocalo Campestre</h1>
                    <p class="hotel-location">📍 Guatapé, Antioquia • Mostrar en el mapa • a 2.1 km del centro</p>
                    <div class="hotel-rating">
                        <span class="rating-score">9.2</span>
                        <span class="rating-text">Fantástico • 478 comentarios</span>
                    </div>
                </div>
                <div class="price-section">
                    <div class="price-amount">$85.000</div>
                    <div class="price-period">por noche</div>
                    <button class="reserve-btn">Reservar ahora</button>
                </div>
            </div>
        </section>

        <!-- Main Content Grid -->
        <div class="main-content">
            <!-- Left Column -->
            <div class="content-column">
                <!-- Description -->
                <section class="content-section">
                    <h2 class="section-title">Acerca de este alojamiento</h2>
                    <p class="description">
                        El Hotel Zocalo Campestre se encuentra en Guatapé, a 70 km de Medellín. Ofrece restaurante, WiFi gratuita y bar.
                        Este establecimiento dispone de habitaciones en la zona principal y bungalows independientes rodeados de jardines
                        tropicales. Todas las habitaciones incluyen TV de pantalla plana, aire acondicionado y baño privado.
                    </p>
                    <br>
                    <p class="description">
                        El hotel cuenta con una hermosa piscina al aire libre con vistas panorámicas al embalse de Guatapé.
                        Los huéspedes pueden disfrutar de actividades como senderismo, pesca y deportes acuáticos.
                        El restaurante sirve cocina local e internacional, y el bar ofrece cócteles y bebidas refrescantes.
                    </p>
                </section>

                <!-- Amenities -->
                <section class="content-section">
                    <h2 class="section-title">Servicios más populares</h2>
                    <div class="amenities-grid">
                        <div class="amenity-item">
                            <div class="amenity-icon"></div>
                            <span class="amenity-text">WiFi gratis</span>
                        </div>
                        <div class="amenity-item">
                            <div class="amenity-icon"></div>
                            <span class="amenity-text">Piscina al aire libre</span>
                        </div>
                        <div class="amenity-item">
                            <div class="amenity-icon"></div>
                            <span class="amenity-text">Parking gratuito</span>
                        </div>
                        <div class="amenity-item">
                            <div class="amenity-icon"></div>
                            <span class="amenity-text">Restaurante</span>
                        </div>
                        <div class="amenity-item">
                            <div class="amenity-icon"></div>
                            <span class="amenity-text">Bar</span>
                        </div>
                        <div class="amenity-item">
                            <div class="amenity-icon"></div>
                            <span class="amenity-text">Aire acondicionado</span>
                        </div>
                        <div class="amenity-item">
                            <div class="amenity-icon"></div>
                            <span class="amenity-text">TV de pantalla plana</span>
                        </div>
                        <div class="amenity-item">
                            <div class="amenity-icon"></div>
                            <span class="amenity-text">Jardín</span>
                        </div>
                        <div class="amenity-item">
                            <div class="amenity-icon"></div>
                            <span class="amenity-text">Terraza</span>
                        </div>
                        <div class="amenity-item">
                            <div class="amenity-icon"></div>
                            <span class="amenity-text">Servicio de habitaciones</span>
                        </div>
                    </div>
                </section>

                <!-- Reviews -->
                <section class="content-section">
                    <h2 class="section-title">Comentarios de huéspedes</h2>

                    <div class="review-item">
                        <div class="review-header">
                            <span class="reviewer-name">María González</span>
                            <span class="review-rating">9.5</span>
                        </div>
                        <p class="review-text">
                            "Excelente hotel con vistas espectaculares al embalse. Las habitaciones son muy cómodas y el personal
                            extremadamente amable. La piscina es perfecta para relajarse y el restaurante tiene comida deliciosa.
                            Definitivamente regresaríamos."
                        </p>
                    </div>

                    <div class="review-item">
                        <div class="review-header">
                            <span class="reviewer-name">Carlos Rodríguez</span>
                            <span class="review-rating">9.0</span>
                        </div>
                        <p class="review-text">
                            "Ubicación perfecta para explorar Guatapé. El hotel está muy bien mantenido y las instalaciones
                            son de primera calidad. El desayuno es variado y delicioso. Muy recomendado para familias."
                        </p>
                    </div>

                    <div class="review-item">
                        <div class="review-header">
                            <span class="reviewer-name">Ana Martínez</span>
                            <span class="review-rating">8.8</span>
                        </div>
                        <p class="review-text">
                            "Hotel muy tranquilo y relajante. Perfecto para desconectarse de la ciudad.
                            Las vistas desde la piscina son increíbles, especialmente al atardecer.
                            El WiFi funciona muy bien en todas las áreas."
                        </p>
                    </div>
                </section>
            </div>

            <!-- Right Sidebar -->
            <div class="sidebar">
                <div class="booking-card">
                    <h3 class="section-title">Reserva tu estancia</h3>

                    <form class="booking-form" id="bookingForm">
                        <div class="form-group">
                            <label class="form-label">Fecha de entrada</label>
                            <input type="date" class="form-input" id="checkinDate" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Fecha de salida</label>
                            <input type="date" class="form-input" id="checkoutDate" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Huéspedes</label>
                            <select class="form-input" id="guestCount">
                                <option value="1">1 adulto</option>
                                <option value="2" selected>2 adultos</option>
                                <option value="3">3 adultos</option>
                                <option value="4">4 adultos</option>
                                <option value="5">5 adultos</option>
                                <option value="6">6 adultos</option>
                            </select>
                        </div>

                        <div class="booking-summary">
                            <div class="summary-row">
                                <span>$85.000 x <span id="nightCount">1</span> noche(s)</span>
                                <span id="subtotal">$85.000</span>
                            </div>
                            <div class="summary-row">
                                <span>Impuestos y tasas</span>
                                <span id="taxes">$8.500</span>
                            </div>
                            <div class="summary-row summary-total">
                                <span>Total</span>
                                <span id="total">$93.500</span>
                            </div>
                        </div>

                        <button type="submit" class="reserve-btn" style="width: 100%;">
                            Reservar ahora
                        </button>
                    </form>

                    <div style="margin-top: 20px; text-align: center; color: #64748b; font-size: 14px;">
                        <p>✓ Cancelación gratuita</p>
                        <p>✓ Sin pagos por adelantado</p>
                        <p>✓ Confirmación inmediata</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Configurar fechas por defecto
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);

            document.getElementById('checkinDate').value = today.toISOString().split('T')[0];
            document.getElementById('checkoutDate').value = tomorrow.toISOString().split('T')[0];

            // Calcular precios dinámicamente
            function updatePricing() {
                const checkin = new Date(document.getElementById('checkinDate').value);
                const checkout = new Date(document.getElementById('checkoutDate').value);
                const nights = Math.max(1, Math.ceil((checkout - checkin) / (1000 * 60 * 60 * 24)));

                const pricePerNight = 85000;
                const subtotal = pricePerNight * nights;
                const taxes = Math.round(subtotal * 0.1);
                const total = subtotal + taxes;

                document.getElementById('nightCount').textContent = nights;
                document.getElementById('subtotal').textContent = '$' + subtotal.toLocaleString();
                document.getElementById('taxes').textContent = '$' + taxes.toLocaleString();
                document.getElementById('total').textContent = '$' + total.toLocaleString();
            }

            // Event listeners para actualizar precios
            document.getElementById('checkinDate').addEventListener('change', updatePricing);
            document.getElementById('checkoutDate').addEventListener('change', updatePricing);

            // Inicializar precios
            updatePricing();

            // Manejar envío del formulario
            document.getElementById('bookingForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const checkin = document.getElementById('checkinDate').value;
                const checkout = document.getElementById('checkoutDate').value;
                const guests = document.getElementById('guestCount').value;
                const total = document.getElementById('total').textContent;

                alert(`GoVoy - Reserva confirmada!\n\nHotel: Hotel Zocalo Campestre\nFechas: ${checkin} al ${checkout}\nHuéspedes: ${guests}\nTotal: ${total}\n\n¡Gracias por elegir GoVoy!`);
            });

            // Manejar botón de reserva en el header
            document.querySelector('.price-section .reserve-btn').addEventListener('click', function() {
                document.getElementById('bookingForm').scrollIntoView({ behavior: 'smooth' });
            });
        });
    </script>
</body>
</html>