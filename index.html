<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GoVoy - Encuentra tu próxima estancia</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8fafc;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, rgba(0,0,0,0.4), rgba(0,0,0,0.6)), url('https://images.unsplash.com/photo-1566073771259-6a8506099945?w=1200&h=600&fit=crop') center/cover;
            color: white;
            padding: 20px 0;
            min-height: 70vh;
            position: relative;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 60px;
            background: rgba(255, 255, 255, 0.95);
            padding: 15px 30px;
            border-radius: 12px;
            backdrop-filter: blur(10px);
        }

        .logo {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
        }

        .logo .go {
            color: #06b6d4;
        }

        .logo .voy {
            color: #f59e0b;
        }

        .nav-links {
            display: flex;
            gap: 30px;
        }

        .nav-links a {
            color: #64748b;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.3s;
            font-weight: 500;
        }

        .nav-links a:hover {
            background-color: #f1f5f9;
            color: #1e293b;
        }

        .hero {
            text-align: center;
            margin-bottom: 60px;
            margin-top: 40px;
        }

        .hero h1 {
            font-size: 56px;
            margin-bottom: 15px;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .hero p {
            font-size: 20px;
            opacity: 0.95;
            font-weight: 400;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        /* Search Form */
        .search-form {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .search-form h2 {
            font-size: 32px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .search-form .subtitle {
            color: #64748b;
            font-size: 16px;
            margin-bottom: 30px;
        }

        .search-row {
            display: flex;
            gap: 20px;
            align-items: end;
            flex-wrap: wrap;
        }

        .search-field {
            flex: 1;
            min-width: 200px;
        }

        .search-field label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
            font-size: 14px;
        }

        .search-field input,
        .search-field select {
            width: 100%;
            padding: 16px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s;
            background: white;
        }

        .search-field input:focus,
        .search-field select:focus {
            outline: none;
            border-color: #a855f7;
            box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.1);
        }

        .search-btn {
            background: linear-gradient(135deg, #a855f7, #8b5cf6);
            color: white;
            border: none;
            padding: 16px 40px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin-top: 20px;
            box-shadow: 0 4px 12px rgba(168, 85, 247, 0.3);
        }

        .search-btn:hover {
            background: linear-gradient(135deg, #9333ea, #7c3aed);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(168, 85, 247, 0.4);
        }

        /* Main Content */
        .main-content {
            display: flex;
            gap: 30px;
            margin-top: 30px;
        }

        /* Filters Sidebar */
        .filters {
            width: 320px;
            background: white;
            padding: 24px;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            height: fit-content;
            border: 1px solid #f1f5f9;
        }

        .filters h3 {
            margin-bottom: 24px;
            color: #1e293b;
            border-bottom: 2px solid #a855f7;
            padding-bottom: 12px;
            font-size: 20px;
            font-weight: 700;
        }

        .filter-group {
            margin-bottom: 28px;
        }

        .filter-group h4 {
            margin-bottom: 16px;
            color: #374151;
            font-size: 16px;
            font-weight: 600;
        }

        .filter-option {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding: 8px 0;
            border-radius: 8px;
            transition: background-color 0.2s;
        }

        .filter-option:hover {
            background-color: #f8fafc;
        }

        .filter-option label {
            display: flex;
            align-items: center;
            cursor: pointer;
            flex: 1;
            font-weight: 500;
            color: #475569;
        }

        .filter-option input[type="checkbox"] {
            margin-right: 12px;
            width: 18px;
            height: 18px;
            accent-color: #a855f7;
        }

        .filter-count {
            color: #94a3b8;
            font-size: 13px;
            font-weight: 500;
            background: #f1f5f9;
            padding: 4px 8px;
            border-radius: 6px;
        }

        /* Results */
        .results {
            flex: 1;
        }

        .results-header {
            background: white;
            padding: 24px;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            margin-bottom: 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid #f1f5f9;
        }

        .results-count {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
        }

        .sort-select {
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            background: white;
            transition: border-color 0.3s;
        }

        .sort-select:focus {
            outline: none;
            border-color: #a855f7;
        }

        .accommodation-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            margin-bottom: 24px;
            overflow: hidden;
            transition: all 0.3s;
            border: 1px solid #f1f5f9;
        }

        .accommodation-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
        }

        .card-content {
            display: flex;
            padding: 24px;
            gap: 24px;
        }

        .card-image {
            width: 220px;
            height: 160px;
            border-radius: 12px;
            object-fit: cover;
        }

        .card-info {
            flex: 1;
        }

        .card-title {
            font-size: 22px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
            transition: color 0.3s;
            cursor: pointer;
        }

        .card-title:hover {
            color: #a855f7;
        }

        .card-title a {
            color: inherit;
            text-decoration: none;
        }

        .card-location {
            color: #64748b;
            margin-bottom: 12px;
            font-weight: 500;
        }

        .card-description {
            color: #475569;
            line-height: 1.6;
            margin-bottom: 16px;
            font-size: 15px;
        }

        .card-amenities {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
            flex-wrap: wrap;
        }

        .amenity {
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            color: #0369a1;
            padding: 6px 12px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
            border: 1px solid #bae6fd;
        }

        .card-rating {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .rating-score {
            background: linear-gradient(135deg, #a855f7, #8b5cf6);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-weight: 700;
            font-size: 14px;
        }

        .rating-text {
            color: #64748b;
            font-size: 14px;
            font-weight: 500;
        }

        .card-price {
            text-align: right;
            padding: 24px;
            border-left: 1px solid #f1f5f9;
            background: linear-gradient(135deg, #fafafa, #f8fafc);
        }

        .price-amount {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 6px;
        }

        .price-period {
            color: #64748b;
            font-size: 14px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .book-btn {
            background: linear-gradient(135deg, #f59e0b, #f97316);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 12px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s;
            font-size: 14px;
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
        }

        .book-btn:hover {
            background: linear-gradient(135deg, #ea580c, #dc2626);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(245, 158, 11, 0.4);
        }

        @media (max-width: 768px) {
            .header {
                min-height: 60vh;
            }

            .hero h1 {
                font-size: 36px;
            }

            .main-content {
                flex-direction: column;
            }

            .filters {
                width: 100%;
            }

            .search-row {
                flex-direction: column;
            }

            .card-content {
                flex-direction: column;
            }

            .card-image {
                width: 100%;
                height: 200px;
            }

            .card-price {
                border-left: none;
                border-top: 1px solid #f1f5f9;
                text-align: left;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="nav">
                <div class="logo"><span class="go">Go</span><span class="voy">Voy</span></div>
                <div class="nav-links">
                    <a href="#">Inicio</a>
                    <a href="#">Sobre Nosotros</a>
                    <a href="#">Contacto</a>
                    <a href="#">Mi Perfil</a>
                    <a href="#">Más Información</a>
                </div>
            </nav>

            <div class="hero">
                <h1>GoVoy Alojamientos</h1>
                <p>La casa del viajero</p>
            </div>

            <!-- Search Form -->
            <form class="search-form" id="searchForm">
                <h2>¡Reserva ahora!</h2>
                <p class="subtitle">Encuentra el alojamiento perfecto para tu próximo viaje</p>
                <div class="search-row">
                    <div class="search-field">
                        <label for="destination">¿Adónde quieres ir?</label>
                        <input type="text" id="destination" placeholder="¿A dónde vas?" value="Guatapé">
                    </div>
                    <div class="search-field">
                        <label for="checkin">Fecha de Entrada</label>
                        <input type="date" id="checkin">
                    </div>
                    <div class="search-field">
                        <label for="checkout">Fecha de Salida</label>
                        <input type="date" id="checkout">
                    </div>
                    <div class="search-field">
                        <label for="guests">Huéspedes</label>
                        <select id="guests">
                            <option value="1">1 adulto</option>
                            <option value="2" selected>2 adultos</option>
                            <option value="3">3 adultos</option>
                            <option value="4">4 adultos</option>
                        </select>
                    </div>
                </div>
                <button type="submit" class="search-btn">Buscar</button>
            </form>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container">
        <div class="main-content">
            <!-- Filters Sidebar -->
            <aside class="filters">
                <h3>Filtrar por:</h3>

                <div class="filter-group">
                    <h4>Filtros populares</h4>
                    <div class="filter-option">
                        <label>
                            <input type="checkbox" name="filter" value="piscina">
                            Piscina
                        </label>
                        <span class="filter-count">31</span>
                    </div>
                    <div class="filter-option">
                        <label>
                            <input type="checkbox" name="filter" value="wifi">
                            WiFi gratis
                        </label>
                        <span class="filter-count">219</span>
                    </div>
                    <div class="filter-option">
                        <label>
                            <input type="checkbox" name="filter" value="casas-chalets">
                            Casas y chalets
                        </label>
                        <span class="filter-count">24</span>
                    </div>
                    <div class="filter-option">
                        <label>
                            <input type="checkbox" name="filter" value="villas">
                            Villas
                        </label>
                        <span class="filter-count">13</span>
                    </div>
                    <div class="filter-option">
                        <label>
                            <input type="checkbox" name="filter" value="tv">
                            TV
                        </label>
                        <span class="filter-count">139</span>
                    </div>
                    <div class="filter-option">
                        <label>
                            <input type="checkbox" name="filter" value="apartamentos">
                            Apartamentos
                        </label>
                        <span class="filter-count">93</span>
                    </div>
                    <div class="filter-option">
                        <label>
                            <input type="checkbox" name="filter" value="parking">
                            Parking
                        </label>
                        <span class="filter-count">172</span>
                    </div>
                    <div class="filter-option">
                        <label>
                            <input type="checkbox" name="filter" value="desayuno">
                            Bed and breakfast
                        </label>
                        <span class="filter-count">3</span>
                    </div>
                </div>

                <div class="filter-group">
                    <h4>Tipo de alojamiento</h4>
                    <div class="filter-option">
                        <label>
                            <input type="checkbox" name="type" value="apartamentos">
                            Apartamentos
                        </label>
                        <span class="filter-count">93</span>
                    </div>
                    <div class="filter-option">
                        <label>
                            <input type="checkbox" name="type" value="hoteles">
                            Hoteles
                        </label>
                        <span class="filter-count">58</span>
                    </div>
                    <div class="filter-option">
                        <label>
                            <input type="checkbox" name="type" value="casas-chalets">
                            Casas y chalets
                        </label>
                        <span class="filter-count">24</span>
                    </div>
                    <div class="filter-option">
                        <label>
                            <input type="checkbox" name="type" value="villas">
                            Villas
                        </label>
                        <span class="filter-count">13</span>
                    </div>
                    <div class="filter-option">
                        <label>
                            <input type="checkbox" name="type" value="campings">
                            Campings
                        </label>
                        <span class="filter-count">12</span>
                    </div>
                </div>
            </aside>

            <!-- Results Section -->
            <main class="results">
                <div class="results-header">
                    <div class="results-count">Guatapé: <span id="resultsCount">235</span> alojamientos encontrados</div>
                    <select class="sort-select" id="sortSelect">
                        <option value="recommended">Ordenar por: Nuestros destacados</option>
                        <option value="price-low">Precio: de menor a mayor</option>
                        <option value="price-high">Precio: de mayor a menor</option>
                        <option value="rating">Puntuación de los huéspedes</option>
                        <option value="distance">Distancia al centro</option>
                    </select>
                </div>

                <div id="accommodationsList">
                    <!-- Hotel Zocalo Campestre -->
                    <div class="accommodation-card" data-type="hoteles" data-amenities="wifi,piscina,parking">
                        <div class="card-content">
                            <img src="https://images.unsplash.com/photo-1566073771259-6a8506099945?w=200&h=150&fit=crop" alt="Hotel Zocalo Campestre" class="card-image">
                            <div class="card-info">
                                <h3 class="card-title">Hotel Zocalo Campestre</h3>
                                <p class="card-location">Guatapé • Mostrar en el mapa • a 2.1 km del centro</p>
                                <p class="card-description">El Hotel Zocalo Campestre se encuentra en Guatapé, a 70 km de Medellín. Ofrece restaurante, WiFi gratuita y bar. Este establecimiento dispone de habitaciones en la zona principal y bungalows.</p>
                                <div class="card-amenities">
                                    <span class="amenity">WiFi gratis</span>
                                    <span class="amenity">Piscina</span>
                                    <span class="amenity">Parking</span>
                                </div>
                                <div class="card-rating">
                                    <span class="rating-score">9.2</span>
                                    <span class="rating-text">Fantástico • 478 comentarios</span>
                                </div>
                            </div>
                            <div class="card-price">
                                <div class="price-amount">$85.000</div>
                                <div class="price-period">por noche</div>
                                <button class="book-btn">Mostrar precios</button>
                            </div>
                        </div>
                    </div>

                    <!-- Hotel Verony Guatape -->
                    <div class="accommodation-card" data-type="hoteles" data-amenities="wifi,piscina,parking,tv">
                        <div class="card-content">
                            <img src="https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=200&h=150&fit=crop" alt="Hotel Verony Guatape" class="card-image">
                            <div class="card-info">
                                <h3 class="card-title">Hotel Verony Guatape</h3>
                                <p class="card-location">Guatapé • Mostrar en el mapa • a 1.8 km del centro</p>
                                <p class="card-description">Hotel Verony Guatape está en Guatapé, a 12 min a pie de Piedra del Peñol, y dispone de alojamiento con piscina al aire libre, parking gratuito privado, jardín y salón de uso común.</p>
                                <div class="card-amenities">
                                    <span class="amenity">WiFi gratis</span>
                                    <span class="amenity">Piscina</span>
                                    <span class="amenity">Parking gratis</span>
                                    <span class="amenity">TV</span>
                                </div>
                                <div class="card-rating">
                                    <span class="rating-score">9.2</span>
                                    <span class="rating-text">Muy bien • 1.234 comentarios</span>
                                </div>
                            </div>
                            <div class="card-price">
                                <div class="price-amount">$95.000</div>
                                <div class="price-period">por noche</div>
                                <button class="book-btn">Mostrar precios</button>
                            </div>
                        </div>
                    </div>

                    <!-- Hotel Los Recuerdos -->
                    <div class="accommodation-card" data-type="hoteles" data-amenities="wifi,parking,tv">
                        <div class="card-content">
                            <img src="https://images.unsplash.com/photo-1564501049412-61c2a3083791?w=200&h=150&fit=crop" alt="Hotel Los Recuerdos" class="card-image">
                            <div class="card-info">
                                <h3 class="card-title">Hotel Los Recuerdos</h3>
                                <p class="card-location">Guatapé • Mostrar en el mapa • a 2.4 km del centro</p>
                                <p class="card-description">El Hotel Los Recuerdos está situado en el centro de Guatapé y ofrece vistas espléndidas de El Peñol, una gran alberca climatizada al aire libre con servicio de bar de bebidas. La WiFi es gratuita.</p>
                                <div class="card-amenities">
                                    <span class="amenity">WiFi gratis</span>
                                    <span class="amenity">Parking</span>
                                    <span class="amenity">TV</span>
                                    <span class="amenity">Bar</span>
                                </div>
                                <div class="card-rating">
                                    <span class="rating-score">8.9</span>
                                    <span class="rating-text">Muy bien • 845 comentarios</span>
                                </div>
                            </div>
                            <div class="card-price">
                                <div class="price-amount">$75.000</div>
                                <div class="price-period">por noche</div>
                                <button class="book-btn">Mostrar precios</button>
                            </div>
                        </div>
                    </div>

                    <!-- Casa Rural El Mirador -->
                    <div class="accommodation-card" data-type="casas-chalets" data-amenities="wifi,piscina,parking">
                        <div class="card-content">
                            <img src="https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?w=200&h=150&fit=crop" alt="Casa Rural El Mirador" class="card-image">
                            <div class="card-info">
                                <h3 class="card-title">Casa Rural El Mirador</h3>
                                <p class="card-location">Guatapé • Mostrar en el mapa • a 3.2 km del centro</p>
                                <p class="card-description">Hermosa casa rural con vista panorámica al embalse. Perfecta para familias, cuenta con piscina privada, jardín amplio y todas las comodidades para una estancia inolvidable.</p>
                                <div class="card-amenities">
                                    <span class="amenity">WiFi gratis</span>
                                    <span class="amenity">Piscina privada</span>
                                    <span class="amenity">Parking</span>
                                    <span class="amenity">Jardín</span>
                                </div>
                                <div class="card-rating">
                                    <span class="rating-score">9.5</span>
                                    <span class="rating-text">Excepcional • 156 comentarios</span>
                                </div>
                            </div>
                            <div class="card-price">
                                <div class="price-amount">$120.000</div>
                                <div class="price-period">por noche</div>
                                <button class="book-btn">Mostrar precios</button>
                            </div>
                        </div>
                    </div>

                    <!-- Apartamento Vista Lago -->
                    <div class="accommodation-card" data-type="apartamentos" data-amenities="wifi,tv,parking">
                        <div class="card-content">
                            <img src="https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=200&h=150&fit=crop" alt="Apartamento Vista Lago" class="card-image">
                            <div class="card-info">
                                <h3 class="card-title">Apartamento Vista Lago</h3>
                                <p class="card-location">Guatapé • Mostrar en el mapa • a 1.5 km del centro</p>
                                <p class="card-description">Moderno apartamento con vista directa al lago. Completamente equipado con cocina, sala de estar y balcón privado. Ideal para parejas o familias pequeñas.</p>
                                <div class="card-amenities">
                                    <span class="amenity">WiFi gratis</span>
                                    <span class="amenity">TV</span>
                                    <span class="amenity">Cocina</span>
                                    <span class="amenity">Balcón</span>
                                </div>
                                <div class="card-rating">
                                    <span class="rating-score">8.7</span>
                                    <span class="rating-text">Muy bien • 298 comentarios</span>
                                </div>
                            </div>
                            <div class="card-price">
                                <div class="price-amount">$65.000</div>
                                <div class="price-period">por noche</div>
                                <button class="book-btn">Mostrar precios</button>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Datos de ejemplo para los alojamientos
        const accommodations = [
            {
                id: 1,
                name: "Hotel Zocalo Campestre",
                type: "hoteles",
                location: "Guatapé",
                distance: "2.1 km del centro",
                description: "El Hotel Zocalo Campestre se encuentra en Guatapé, a 70 km de Medellín. Ofrece restaurante, WiFi gratuita y bar.",
                amenities: ["wifi", "piscina", "parking"],
                rating: 9.2,
                reviews: 478,
                price: 85000,
                image: "https://images.unsplash.com/photo-1566073771259-6a8506099945?w=200&h=150&fit=crop"
            },
            {
                id: 2,
                name: "Hotel Verony Guatape",
                type: "hoteles",
                location: "Guatapé",
                distance: "1.8 km del centro",
                description: "Hotel Verony Guatape está en Guatapé, a 12 min a pie de Piedra del Peñol, y dispone de alojamiento con piscina al aire libre.",
                amenities: ["wifi", "piscina", "parking", "tv"],
                rating: 9.2,
                reviews: 1234,
                price: 95000,
                image: "https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=200&h=150&fit=crop"
            },
            {
                id: 3,
                name: "Hotel Los Recuerdos",
                type: "hoteles",
                location: "Guatapé",
                distance: "2.4 km del centro",
                description: "El Hotel Los Recuerdos está situado en el centro de Guatapé y ofrece vistas espléndidas de El Peñol.",
                amenities: ["wifi", "parking", "tv"],
                rating: 8.9,
                reviews: 845,
                price: 75000,
                image: "https://images.unsplash.com/photo-1564501049412-61c2a3083791?w=200&h=150&fit=crop"
            },
            {
                id: 4,
                name: "Casa Rural El Mirador",
                type: "casas-chalets",
                location: "Guatapé",
                distance: "3.2 km del centro",
                description: "Hermosa casa rural con vista panorámica al embalse. Perfecta para familias, cuenta con piscina privada.",
                amenities: ["wifi", "piscina", "parking"],
                rating: 9.5,
                reviews: 156,
                price: 120000,
                image: "https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?w=200&h=150&fit=crop"
            },
            {
                id: 5,
                name: "Apartamento Vista Lago",
                type: "apartamentos",
                location: "Guatapé",
                distance: "1.5 km del centro",
                description: "Moderno apartamento con vista directa al lago. Completamente equipado con cocina, sala de estar y balcón privado.",
                amenities: ["wifi", "tv", "parking"],
                rating: 8.7,
                reviews: 298,
                price: 65000,
                image: "https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=200&h=150&fit=crop"
            }
        ];

        let filteredAccommodations = [...accommodations];

        // Función para renderizar las tarjetas de alojamiento
        function renderAccommodations(accommodationsList) {
            const container = document.getElementById('accommodationsList');
            container.innerHTML = '';

            accommodationsList.forEach(accommodation => {
                const amenityTags = accommodation.amenities.map(amenity => {
                    const amenityNames = {
                        'wifi': 'WiFi gratis',
                        'piscina': 'Piscina',
                        'parking': 'Parking',
                        'tv': 'TV',
                        'desayuno': 'Desayuno'
                    };
                    return `<span class="amenity">${amenityNames[amenity] || amenity}</span>`;
                }).join('');

                // Determinar el enlace del hotel
                const hotelLink = accommodation.name === "Hotel Zocalo Campestre" ? "hotel_landing.html" : "#";

                const card = `
                    <div class="accommodation-card" data-type="${accommodation.type}" data-amenities="${accommodation.amenities.join(',')}">
                        <div class="card-content">
                            <img src="${accommodation.image}" alt="${accommodation.name}" class="card-image">
                            <div class="card-info">
                                <h3 class="card-title">
                                    <a href="${hotelLink}">${accommodation.name}</a>
                                </h3>
                                <p class="card-location">${accommodation.location} • Mostrar en el mapa • a ${accommodation.distance}</p>
                                <p class="card-description">${accommodation.description}</p>
                                <div class="card-amenities">
                                    ${amenityTags}
                                </div>
                                <div class="card-rating">
                                    <span class="rating-score">${accommodation.rating}</span>
                                    <span class="rating-text">Muy bien • ${accommodation.reviews} comentarios</span>
                                </div>
                            </div>
                            <div class="card-price">
                                <div class="price-amount">$${accommodation.price.toLocaleString()}</div>
                                <div class="price-period">por noche</div>
                                <button class="book-btn">Mostrar precios</button>
                            </div>
                        </div>
                    </div>
                `;
                container.innerHTML += card;
            });

            // Actualizar contador de resultados
            document.getElementById('resultsCount').textContent = accommodationsList.length;
        }

        // Función para aplicar filtros
        function applyFilters() {
            const checkedFilters = Array.from(document.querySelectorAll('input[type="checkbox"]:checked'))
                .map(checkbox => checkbox.value);

            if (checkedFilters.length === 0) {
                filteredAccommodations = [...accommodations];
            } else {
                filteredAccommodations = accommodations.filter(accommodation => {
                    // Verificar si el alojamiento coincide con algún filtro de tipo
                    const typeMatch = checkedFilters.some(filter => accommodation.type === filter);

                    // Verificar si el alojamiento tiene alguna de las amenidades seleccionadas
                    const amenityMatch = checkedFilters.some(filter => accommodation.amenities.includes(filter));

                    return typeMatch || amenityMatch;
                });
            }

            renderAccommodations(filteredAccommodations);
        }

        // Función para ordenar resultados
        function sortResults(sortBy) {
            let sorted = [...filteredAccommodations];

            switch (sortBy) {
                case 'price-low':
                    sorted.sort((a, b) => a.price - b.price);
                    break;
                case 'price-high':
                    sorted.sort((a, b) => b.price - a.price);
                    break;
                case 'rating':
                    sorted.sort((a, b) => b.rating - a.rating);
                    break;
                case 'distance':
                    sorted.sort((a, b) => parseFloat(a.distance) - parseFloat(b.distance));
                    break;
                default:
                    // Mantener orden recomendado (por defecto)
                    break;
            }

            renderAccommodations(sorted);
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Renderizar alojamientos iniciales
            renderAccommodations(accommodations);

            // Configurar fechas por defecto
            const today = new Date();
            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);

            document.getElementById('checkin').value = today.toISOString().split('T')[0];
            document.getElementById('checkout').value = tomorrow.toISOString().split('T')[0];

            // Event listeners para filtros
            document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
                checkbox.addEventListener('change', applyFilters);
            });

            // Event listener para ordenamiento
            document.getElementById('sortSelect').addEventListener('change', function() {
                sortResults(this.value);
            });

            // Event listener para formulario de búsqueda
            document.getElementById('searchForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const destination = document.getElementById('destination').value;
                const checkin = document.getElementById('checkin').value;
                const checkout = document.getElementById('checkout').value;
                const guests = document.getElementById('guests').value;

                // Aquí podrías implementar la lógica de búsqueda real
                console.log('Búsqueda GoVoy:', { destination, checkin, checkout, guests });

                // Simular nueva búsqueda
                alert(`GoVoy - Buscando alojamientos en ${destination} para ${guests} huéspedes del ${checkin} al ${checkout}`);
            });

            // Event listeners para botones de reserva
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('book-btn')) {
                    const card = e.target.closest('.accommodation-card');
                    const title = card.querySelector('.card-title').textContent;
                    alert(`GoVoy - Redirigiendo a la página de reserva para: ${title}`);
                }
            });
        });
    </script>
</body>
</html>